import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../config/app_config.dart';
import '../utils/logger.dart';
import 'storage_service.dart';

part 'firebase_functions_service.g.dart';

/// Service for calling Firebase Functions
class FirebaseFunctionsService {
  late final Dio _dio;
  final StorageService _storageService;

  FirebaseFunctionsService(this._storageService) {
    _dio = Dio();
    _setupDio();
  }

  void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: AppConfig.functionsBaseUrl,
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add auth interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          try {
            // Get Firebase Auth token
            final user = FirebaseAuth.instance.currentUser;
            if (user != null) {
              final token = await user.getIdToken();
              options.headers['Authorization'] = 'Bearer $token';
            }
          } catch (e) {
            AppLogger.warning('Failed to get auth token: $e');
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          AppLogger.info('Firebase Functions Response: ${response.statusCode} ${response.requestOptions.path}');
          handler.next(response);
        },
        onError: (error, handler) {
          AppLogger.warning('Firebase Functions Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  /// Generate meal plan using Firebase Functions
  Future<Map<String, dynamic>> generateMealPlan({
    required Map<String, dynamic> preferences,
    int duration = 7,
  }) async {
    try {
      AppLogger.info('Generating meal plan with preferences: $preferences');

      final response = await _dio.post<Map<String, dynamic>>(
        '/meal-plans/generate',
        data: {
          'preferences': preferences,
          'duration': duration,
        },
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Meal plan generated successfully');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in generateMealPlan: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in generateMealPlan: $e');
      throw Exception('Failed to generate meal plan: $e');
    }
  }

  /// Analyze nutrition using Firebase Functions
  Future<Map<String, dynamic>> analyzeNutrition({
    required List<String> foodItems,
    required String portion,
  }) async {
    try {
      AppLogger.info('Analyzing nutrition for: $foodItems');

      final response = await _dio.post<Map<String, dynamic>>(
        '/analyze-nutrition',
        data: {
          'foodItems': foodItems,
          'portion': portion,
        },
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Nutrition analysis completed successfully');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in analyzeNutrition: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in analyzeNutrition: $e');
      throw Exception('Failed to analyze nutrition: $e');
    }
  }

  /// Get user's meal plans
  Future<Map<String, dynamic>> getMealPlans({
    int limit = 10,
    String status = 'active',
  }) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('Fetching meal plans for user: ${user.uid}');

      final response = await _dio.get<Map<String, dynamic>>(
        '/meal-plans/${user.uid}',
        queryParameters: {
          'limit': limit,
          'status': status,
        },
      );

      if (response.data == null) {
        throw Exception('No data received from server');
      }

      AppLogger.info('Meal plans fetched successfully');
      return response.data!;
    } on DioException catch (e) {
      AppLogger.warning('DioException in getMealPlans: ${e.message}');
      throw _handleDioException(e);
    } catch (e) {
      AppLogger.warning('Exception in getMealPlans: $e');
      throw Exception('Failed to fetch meal plans: $e');
    }
  }

  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['error'] ?? e.message;

        switch (statusCode) {
          case 400:
            return Exception('Invalid request: $message');
          case 401:
            return Exception('Authentication failed. Please sign in again.');
          case 403:
            return Exception('Access denied: $message');
          case 404:
            return Exception('Service not found: $message');
          case 500:
            return Exception('Server error: $message');
          default:
            return Exception('Request failed ($statusCode): $message');
        }

      case DioExceptionType.cancel:
        return Exception('Request was cancelled');

      case DioExceptionType.connectionError:
        return Exception('No internet connection');

      default:
        return Exception('Network error: ${e.message}');
    }
  }

  void dispose() {
    _dio.close();
  }
}

@riverpod
FirebaseFunctionsService firebaseFunctionsService(FirebaseFunctionsServiceRef ref) {
  final storageService = ref.watch(storageServiceProvider);
  return FirebaseFunctionsService(storageService);
}
