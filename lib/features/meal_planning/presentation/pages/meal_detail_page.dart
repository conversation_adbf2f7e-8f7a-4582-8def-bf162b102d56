import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';

class MealDetailPage extends ConsumerWidget {
  const MealDetailPage({
    super.key,
    required this.mealId,
  });

  final String mealId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الوجبة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite_border),
            onPressed: () {
              // TODO: Add to favorites
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: Share meal
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Meal Image
            Container(
              width: double.infinity,
              height: 250,
              decoration: const BoxDecoration(
                color: AppColors.surface,
                image: DecorationImage(
                  image: NetworkImage('https://via.placeholder.com/400x250'),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Meal Name and Info
                  Text(
                    'سلطة الدجاج المشوي',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildInfoChip(context, '450 سعرة', Icons.local_fire_department),
                      const SizedBox(width: 8),
                      _buildInfoChip(context, '30 دقيقة', Icons.access_time),
                      const SizedBox(width: 8),
                      _buildInfoChip(context, 'سهل', Icons.star),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Nutrition Info
                  Text(
                    'المعلومات الغذائية',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNutritionCard(context, 'البروتين', '35g', AppColors.protein),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNutritionCard(context, 'الكربوهيدرات', '20g', AppColors.carbs),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildNutritionCard(context, 'الدهون', '25g', AppColors.fat),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNutritionCard(context, 'الألياف', '8g', AppColors.fiber),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Ingredients
                  Text(
                    'المكونات',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildIngredientsList(context),

                  const SizedBox(height: 24),

                  // Instructions
                  Text(
                    'طريقة التحضير',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInstructionsList(context),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            top: BorderSide(color: AppColors.border),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // TODO: Add to meal plan
                },
                child: const Text('إضافة للخطة'),
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: () {
                // TODO: Start cooking timer
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
              ),
              child: const Icon(Icons.timer),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(BuildContext context, String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.primaryWithOpacity,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(width: 4),
          Text(
            text,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionCard(BuildContext context, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIngredientsList(BuildContext context) {
    final ingredients = [
      '200g صدر دجاج مشوي',
      '100g خس مقطع',
      '50g طماطم كرزية',
      '30g جبن فيتا',
      '2 ملعقة كبيرة زيت زيتون',
      '1 ملعقة كبيرة خل بلسمي',
      'ملح وفلفل حسب الذوق',
    ];

    return Column(
      children: ingredients.map((ingredient) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Container(
                width: 6,
                height: 6,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  ingredient,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildInstructionsList(BuildContext context) {
    final instructions = [
      'اشوي صدر الدجاج في مقلاة على نار متوسطة لمدة 6-8 دقائق من كل جانب',
      'اتركي الدجاج يبرد ثم قطعيه إلى شرائح',
      'اغسلي الخس واتركيه ينشف، ثم قطعيه',
      'اقطعي الطماطم الكرزية إلى نصفين',
      'في وعاء كبير، اخلطي الخس والطماطم والدجاج',
      'أضيفي الجبن الفيتا المفتت',
      'اخلطي زيت الزيتون والخل البلسمي والملح والفلفل',
      'اسكبي الصلصة على السلطة وقلبي برفق',
    ];

    return Column(
      children: instructions.asMap().entries.map((entry) {
        final index = entry.key + 1;
        final instruction = entry.value;

        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '$index',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  instruction,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
