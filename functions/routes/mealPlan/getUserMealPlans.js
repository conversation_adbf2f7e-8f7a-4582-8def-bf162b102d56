const {authenticateUser} = require('../../middleware/auth');
const {mealPlansQuerySchema} = require('../../utils/validators');
const {getUserMealPlans} = require('../../services/mealPlanService');

/**
 * GET /meal-plans/:userId
 * Retrieves user's meal plans
 * Requires authentication
 */
async function getMealPlansByUserId(req, res) {
  try {
    const {userId} = req.params;

    // Validate query parameters
    const {error, value} = mealPlansQuerySchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    // Ensure user can only access their own meal plans
    if (req.body.userId !== userId) {
      return res.status(403).json({
        error: 'Forbidden: Cannot access other user\'s meal plans',
      });
    }

    const mealPlans = await getUserMealPlans(userId, value);

    res.json({
      success: true,
      mealPlans,
      count: mealPlans.length,
    });
  } catch (error) {
    console.error('Error fetching meal plans:', error);
    res.status(500).json({
      error: 'Failed to fetch meal plans',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'get',
  path: '/meal-plans/:userId',
  middleware: [authenticateUser],
  handler: getMealPlansByUserId,
};
