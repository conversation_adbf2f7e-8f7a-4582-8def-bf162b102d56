const express = require('express');

// Import endpoint configurations
const generatePublic = require('./generatePublic');
const generateAuth = require('./generateAuth');
const getUserMealPlans = require('./getUserMealPlans');

const router = express.Router();

// Register routes
const endpoints = [
  generatePublic,
  generateAuth,
  getUserMealPlans,
];

endpoints.forEach((endpoint) => {
  const {method, path, middleware = [], handler} = endpoint;
  router[method](path, ...middleware, handler);
});

module.exports = router;
