const {mealPlanRequestSchema} = require('../../utils/validators');
const {
  generateMealPlan,
  saveMealPlan,
} = require('../../services/mealPlanService');

/**
 * POST /generate-meal-plan
 * Generates a new meal plan using OpenAI
 * Public endpoint for testing (no authentication required)
 */
async function generatePublicMealPlan(req, res) {
  try {
    // Validate request
    const {error, value} = mealPlanRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {userId, preferences, duration} = value;

    // Generate meal plan
    const mealPlan = await generateMealPlan(preferences, duration, userId);

    // Save to Firestore
    await saveMealPlan(userId, mealPlan, preferences, duration);

    res.json({
      success: true,
      mealPlan,
      message: 'Meal plan generated successfully',
    });
  } catch (error) {
    console.error('Error generating meal plan:', error);
    res.status(500).json({
      error: 'Failed to generate meal plan',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'post',
  path: '/generate-meal-plan',
  middleware: [],
  handler: generatePublicMealPlan,
};
