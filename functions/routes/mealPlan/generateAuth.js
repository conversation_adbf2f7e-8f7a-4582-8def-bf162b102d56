const {authenticateUser} = require('../../middleware/auth');
const {mealPlanRequestSchema} = require('../../utils/validators');
const {
  generateMealPlan,
  saveMealPlan,
} = require('../../services/mealPlanService');

/**
 * POST /meal-plans/generate
 * Authenticated version of meal plan generation
 * Requires authentication
 */
async function generateAuthenticatedMealPlan(req, res) {
  try {
    // Validate request
    const {error, value} = mealPlanRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {preferences, duration} = value;
    const userId = req.body.userId; // From auth middleware

    // Generate meal plan
    const mealPlan = await generateMealPlan(preferences, duration, userId);

    // Save to Firestore
    await saveMealPlan(userId, mealPlan, preferences, duration);

    res.json({
      success: true,
      mealPlan,
      message: 'Meal plan generated successfully',
    });
  } catch (error) {
    console.error('Error generating meal plan:', error);
    res.status(500).json({
      error: 'Failed to generate meal plan',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

module.exports = {
  method: 'post',
  path: '/meal-plans/generate',
  middleware: [authenticateUser],
  handler: generateAuthenticatedMealPlan,
};
