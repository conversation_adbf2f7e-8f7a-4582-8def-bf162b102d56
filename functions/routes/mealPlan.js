const express = require('express');
const {authenticateUser} = require('../middleware/auth');
const {mealPlanRequestSchema, mealPlansQuerySchema} = require('../utils/validators');
const {
  generateMealPlan,
  saveMealPlan,
  getUserMealPlans,
} = require('../services/mealPlanService');

const router = express.Router();

/**
 * POST /generate-meal-plan
 * Generates a new meal plan using OpenAI
 * Public endpoint for testing (no authentication required)
 */
router.post('/generate-meal-plan', async (req, res) => {
  try {
    // Validate request
    const {error, value} = mealPlanRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {userId, preferences, duration} = value;

    // Generate meal plan
    const mealPlan = await generateMealPlan(preferences, duration, userId);

    // Save to Firestore
    await saveMealPlan(userId, mealPlan, preferences, duration);

    res.json({
      success: true,
      mealPlan,
      message: 'Meal plan generated successfully',
    });
  } catch (error) {
    console.error('Error generating meal plan:', error);
    res.status(500).json({
      error: 'Failed to generate meal plan',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /meal-plans/:userId
 * Retrieves user's meal plans
 * Requires authentication
 */
router.get('/meal-plans/:userId', authenticateUser, async (req, res) => {
  try {
    const {userId} = req.params;

    // Validate query parameters
    const {error, value} = mealPlansQuerySchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    // Ensure user can only access their own meal plans
    if (req.body.userId !== userId) {
      return res.status(403).json({
        error: 'Forbidden: Cannot access other user\'s meal plans',
      });
    }

    const mealPlans = await getUserMealPlans(userId, value);

    res.json({
      success: true,
      mealPlans,
      count: mealPlans.length,
    });
  } catch (error) {
    console.error('Error fetching meal plans:', error);
    res.status(500).json({
      error: 'Failed to fetch meal plans',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /meal-plans/generate
 * Authenticated version of meal plan generation
 * Requires authentication
 */
router.post('/meal-plans/generate', authenticateUser, async (req, res) => {
  try {
    // Validate request
    const {error, value} = mealPlanRequestSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {preferences, duration} = value;
    const userId = req.body.userId; // From auth middleware

    // Generate meal plan
    const mealPlan = await generateMealPlan(preferences, duration, userId);

    // Save to Firestore
    await saveMealPlan(userId, mealPlan, preferences, duration);

    res.json({
      success: true,
      mealPlan,
      message: 'Meal plan generated successfully',
    });
  } catch (error) {
    console.error('Error generating meal plan:', error);
    res.status(500).json({
      error: 'Failed to generate meal plan',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

module.exports = router;
