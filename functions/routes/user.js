const express = require('express');
const {authenticateUser} = require('../middleware/auth');
const {
  getUserProfile,
  updateUserProfile,
} = require('../services/userService');

const router = express.Router();

/**
 * GET /user/profile
 * Gets the authenticated user's profile
 * Requires authentication
 */
router.get('/user/profile', authenticateUser, async (req, res) => {
  try {
    const userId = req.body.userId; // From auth middleware

    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      return res.status(404).json({
        error: 'User profile not found',
      });
    }

    res.json({
      success: true,
      profile: userProfile,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      error: 'Failed to fetch user profile',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * PUT /user/profile
 * Updates the authenticated user's profile
 * Requires authentication
 */
router.put('/user/profile', authenticateUser, async (req, res) => {
  try {
    const userId = req.body.userId; // From auth middleware

    // Extract update fields (exclude userId and sensitive fields)
    const {
      displayName,
      photoURL,
      onboardingCompleted,
      preferences,
      ...otherUpdates
    } = req.body;

    const allowedUpdates = {
      displayName,
      photoURL,
      onboardingCompleted,
      preferences,
      ...otherUpdates,
    };

    // Remove undefined values
    Object.keys(allowedUpdates).forEach((key) => {
      if (allowedUpdates[key] === undefined) {
        delete allowedUpdates[key];
      }
    });

    if (Object.keys(allowedUpdates).length === 0) {
      return res.status(400).json({
        error: 'No valid fields to update',
      });
    }

    await updateUserProfile(userId, allowedUpdates);

    res.json({
      success: true,
      message: 'Profile updated successfully',
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({
      error: 'Failed to update user profile',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /user/:userId/profile
 * Gets a specific user's public profile (admin only or public fields)
 * Requires authentication
 */
router.get('/user/:userId/profile', authenticateUser, async (req, res) => {
  try {
    const {userId} = req.params;
    const requestingUserId = req.body.userId; // From auth middleware

    // Only allow users to access their own profile for now
    // In the future, you might want to add admin checks or public profile fields
    if (requestingUserId !== userId) {
      return res.status(403).json({
        error: 'Forbidden: Cannot access other user\'s profile',
      });
    }

    const userProfile = await getUserProfile(userId);

    if (!userProfile) {
      return res.status(404).json({
        error: 'User profile not found',
      });
    }

    res.json({
      success: true,
      profile: userProfile,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      error: 'Failed to fetch user profile',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

module.exports = router;
