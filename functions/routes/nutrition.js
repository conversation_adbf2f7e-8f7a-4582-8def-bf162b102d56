const express = require('express');
const {authenticateUser} = require('../middleware/auth');
const {nutritionAnalysisSchema} = require('../utils/validators');
const {
  analyzeNutrition,
  calculateNutritionGoals,
  validateNutritionGoals,
} = require('../services/nutritionService');

const router = express.Router();

/**
 * POST /analyze-nutrition
 * Analyzes nutrition content of food items using OpenAI
 * Requires authentication
 */
router.post('/analyze-nutrition', authenticateUser, async (req, res) => {
  try {
    // Validate request
    const {error, value} = nutritionAnalysisSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: error.details[0].message,
      });
    }

    const {foodItems, portion} = value;

    // Analyze nutrition
    const nutritionData = await analyzeNutrition(foodItems, portion);

    res.json({
      success: true,
      nutrition: nutritionData,
      message: 'Nutrition analysis completed',
    });
  } catch (error) {
    console.error('Error analyzing nutrition:', error);
    res.status(500).json({
      error: 'Failed to analyze nutrition',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /nutrition/analyze-with-goals
 * Analyzes nutrition and compares against user goals
 * Requires authentication
 */
router.post('/nutrition/analyze-with-goals', authenticateUser, async (req, res) => {
  try {
    // Validate nutrition analysis request
    const {error: nutritionError} =
      nutritionAnalysisSchema.validate(req.body);

    if (nutritionError) {
      return res.status(400).json({
        error: nutritionError.details[0].message,
      });
    }

    const {foodItems, portion, userPreferences} = req.body;

    // Analyze nutrition
    const nutritionData = await analyzeNutrition(foodItems, portion);

    // Calculate nutrition goals if user preferences provided
    let goalComparison = null;
    if (userPreferences) {
      const goals = calculateNutritionGoals(userPreferences);
      goalComparison = validateNutritionGoals(nutritionData, goals);
    }

    res.json({
      success: true,
      nutrition: nutritionData,
      goalComparison,
      message: 'Nutrition analysis with goals completed',
    });
  } catch (error) {
    console.error('Error analyzing nutrition with goals:', error);
    res.status(500).json({
      error: 'Failed to analyze nutrition with goals',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /nutrition/calculate-goals
 * Calculates nutrition goals based on user preferences
 * Requires authentication
 */
router.post('/nutrition/calculate-goals', authenticateUser, async (req, res) => {
  try {
    const {userPreferences} = req.body;

    if (!userPreferences || !userPreferences.calorieGoal) {
      return res.status(400).json({
        error: 'User preferences with calorie goal are required',
      });
    }

    const goals = calculateNutritionGoals(userPreferences);

    res.json({
      success: true,
      goals,
      message: 'Nutrition goals calculated successfully',
    });
  } catch (error) {
    console.error('Error calculating nutrition goals:', error);
    res.status(500).json({
      error: 'Failed to calculate nutrition goals',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

module.exports = router;
