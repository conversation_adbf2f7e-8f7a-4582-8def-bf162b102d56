const functions = require('firebase-functions');
const express = require('express');
const cors = require('cors');

// Initialize Firebase
const {initializeFirebase} = require('./config/firebase');
initializeFirebase();

// Import routes
const apiRoutes = require('./routes');

// Import triggers
const {onUserCreated, onUserDeleted} = require('./triggers');

// Initialize Express app with CORS
const app = express();
app.use(cors({origin: true}));
app.use(express.json());

// API routes
app.use('/', apiRoutes);

// Error handling middleware
app.use((error, req, res, _next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    details: error instanceof Error ? error.message : 'Unknown error',
  });
});

// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);

// Export Firebase Auth triggers
exports.onUserCreated = onUserCreated;
exports.onUserDeleted = onUserDeleted;
