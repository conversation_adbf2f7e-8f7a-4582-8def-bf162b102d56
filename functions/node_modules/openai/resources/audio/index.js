"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Translations = exports.Transcriptions = exports.Speech = exports.Audio = void 0;
var audio_1 = require("./audio.js");
Object.defineProperty(exports, "Audio", { enumerable: true, get: function () { return audio_1.Audio; } });
var speech_1 = require("./speech.js");
Object.defineProperty(exports, "Speech", { enumerable: true, get: function () { return speech_1.Speech; } });
var transcriptions_1 = require("./transcriptions.js");
Object.defineProperty(exports, "Transcriptions", { enumerable: true, get: function () { return transcriptions_1.Transcriptions; } });
var translations_1 = require("./translations.js");
Object.defineProperty(exports, "Translations", { enumerable: true, get: function () { return translations_1.Translations; } });
//# sourceMappingURL=index.js.map