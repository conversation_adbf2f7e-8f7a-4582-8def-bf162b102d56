{"name": "easydietai-functions", "description": "Firebase Functions for EasyDietAI with OpenAI integration", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^11.8.0", "firebase-functions": "^4.3.1", "openai": "^4.20.1", "cors": "^2.8.5", "express": "^4.18.2", "joi": "^17.9.2", "lodash": "^4.17.21"}, "devDependencies": {"eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0"}, "private": true}