/**
 * Parses meal plan text response from OpenAI
 * @param {string} mealPlanText - Raw response from OpenAI
 * @param {Object} preferences - User preferences for fallback
 * @param {number} duration - Duration for fallback structure
 * @return {Object} Parsed meal plan object
 */
function parseMealPlan(mealPlanText, preferences, duration) {
  try {
    // Try to parse as J<PERSON>N first
    return JSON.parse(mealPlanText);
  } catch (error) {
    // If JSON parsing fails, create a structured response
    console.warn('Failed to parse meal plan as JSON, creating fallback structure');
    return {
      days: Array.from({length: duration}, (_, i) => ({
        day: i + 1,
        meals: [],
        totalNutrition: {
          calories: preferences.calorieGoal || 2000,
          protein: 0,
          carbs: 0,
          fat: 0,
          fiber: 0,
        },
      })),
      rawResponse: mealPlanText,
    };
  }
}

/**
 * Parses nutrition analysis text response from OpenAI
 * @param {string} nutritionText - Raw response from OpenAI
 * @return {Object} Parsed nutrition data object
 */
function parseNutritionData(nutritionText) {
  try {
    return JSON.parse(nutritionText);
  } catch (error) {
    console.warn('Failed to parse nutrition data as JSON, creating fallback structure');
    return {
      totalNutrition: {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
      },
      breakdown: [],
      healthScore: 0,
      recommendations: [],
      rawResponse: nutritionText,
    };
  }
}

/**
 * Safely extracts content from OpenAI completion response
 * @param {Object} completion - OpenAI completion response
 * @return {string|null} Extracted content or null
 */
function extractCompletionContent(completion) {
  return completion.choices[0] &&
         completion.choices[0].message &&
         completion.choices[0].message.content;
}

module.exports = {
  parseMealPlan,
  parseNutritionData,
  extractCompletionContent,
};
