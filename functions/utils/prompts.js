/**
 * Creates a meal plan generation prompt for OpenAI
 * @param {Object} preferences - User dietary preferences
 * @param {number} duration - Number of days for the meal plan
 * @return {string} Formatted prompt for OpenAI
 */
function createMealPlanPrompt(preferences, duration) {
  const {
    dietaryRestrictions,
    allergies,
    cuisinePreferences,
    mealsPerDay,
    calorieGoal,
  } = preferences;

  return `
Generate a ${duration}-day meal plan with the following requirements:

**Dietary Requirements:**
- Daily calorie target: ${calorieGoal} calories
- Meals per day: ${mealsPerDay}
- Dietary restrictions: ${dietaryRestrictions.length > 0 ?
    dietaryRestrictions.join(', ') : 'None'}
- Allergies: ${allergies.length > 0 ? allergies.join(', ') : 'None'}
- Preferred cuisines: ${cuisinePreferences.length > 0 ?
    cuisinePreferences.join(', ') : 'Any'}

**Requirements:**
1. Provide balanced nutrition with appropriate macronutrient distribution
2. Include variety in ingredients and cooking methods
3. Ensure meals are practical and achievable
4. Include estimated preparation time for each meal
5. Provide nutritional breakdown for each meal (calories, protein, carbs, fat, fiber)

**Format the response as JSON with this structure:**
{
  "days": [
    {
      "day": 1,
      "meals": [
        {
          "type": "breakfast",
          "name": "Meal Name",
          "ingredients": ["ingredient1", "ingredient2"],
          "instructions": "Cooking instructions",
          "prepTime": "15 minutes",
          "nutrition": {
            "calories": 400,
            "protein": 20,
            "carbs": 45,
            "fat": 15,
            "fiber": 8
          }
        }
      ],
      "totalNutrition": {
        "calories": 2000,
        "protein": 150,
        "carbs": 200,
        "fat": 65,
        "fiber": 30
      }
    }
  ]
}
`;
}

/**
 * Creates a nutrition analysis prompt for OpenAI
 * @param {string[]} foodItems - Array of food items to analyze
 * @param {string} portion - Portion size description
 * @return {string} Formatted prompt for OpenAI
 */
function createNutritionAnalysisPrompt(foodItems, portion) {
  return `
Analyze the nutritional content of the following food items for ${portion}:

Food items: ${foodItems.join(', ')}

Provide a detailed nutritional breakdown in JSON format:
{
  "totalNutrition": {
    "calories": 0,
    "protein": 0,
    "carbs": 0,
    "fat": 0,
    "fiber": 0,
    "sugar": 0,
    "sodium": 0,
    "cholesterol": 0,
    "vitaminC": 0,
    "calcium": 0,
    "iron": 0
  },
  "breakdown": [
    {
      "item": "food item name",
      "nutrition": {
        "calories": 0,
        "protein": 0,
        "carbs": 0,
        "fat": 0,
        "fiber": 0
      }
    }
  ],
  "healthScore": 85,
  "recommendations": ["recommendation1", "recommendation2"]
}
`;
}

module.exports = {
  createMealPlanPrompt,
  createNutritionAnalysisPrompt,
};
