const Joi = require('joi');

// Meal plan request validation schema
const mealPlanRequestSchema = Joi.object({
  userId: Joi.string().optional().default('test-user'), // Made optional for testing
  preferences: Joi.object({
    dietaryRestrictions: Joi.array().items(Joi.string()).default([]),
    allergies: Joi.array().items(Joi.string()).default([]),
    cuisinePreferences: Joi.array().items(Joi.string()).default([]),
    mealsPerDay: Joi.number().min(1).max(6).default(3),
    calorieGoal: Joi.number().min(1000).max(5000).required(),
    proteinGoal: Joi.number().min(0).default(0),
    carbsGoal: Joi.number().min(0).default(0),
    fatGoal: Joi.number().min(0).default(0),
  }).required(),
  duration: Joi.number().min(1).max(30).default(7), // days
});

// Nutrition analysis request validation schema
const nutritionAnalysisSchema = Joi.object({
  foodItems: Joi.array().items(Joi.string()).min(1).required(),
  portion: Joi.string().default('1 serving'),
});

// User meal plans query validation schema
const mealPlansQuerySchema = Joi.object({
  limit: Joi.number().min(1).max(100).default(10),
  status: Joi.string().valid('active', 'inactive', 'completed').default('active'),
});

module.exports = {
  mealPlanRequestSchema,
  nutritionAnalysisSchema,
  mealPlansQuerySchema,
};
