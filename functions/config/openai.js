const {OpenAI} = require('openai');
const functions = require('firebase-functions');

// OpenAI instance (lazy-loaded singleton)
let openai;

function getOpenAI() {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY ||
      (functions.config().openai && functions.config().openai.api_key);

    if (!apiKey) {
      throw new Error('OpenAI API key not found in environment variables or Firebase config');
    }

    openai = new OpenAI({apiKey});
    console.log('OpenAI client initialized');
  }
  return openai;
}

module.exports = {
  getOpenAI,
};
