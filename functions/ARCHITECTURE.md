# Firebase Functions Clean Code Architecture

This document describes the clean code architecture implemented for the EasyDietAI Firebase Functions.

## 📁 Directory Structure

```
functions/
├── index.js                    # Main entry point
├── config/                     # Configuration files
│   ├── firebase.js            # Firebase Admin initialization
│   └── openai.js              # OpenAI client configuration
├── middleware/                 # Express middleware
│   └── auth.js                # Authentication middleware
├── routes/                     # API route handlers
│   ├── mealPlan.js            # Meal plan endpoints
│   ├── nutrition.js           # Nutrition analysis endpoints
│   └── user.js                # User management endpoints
├── services/                   # Business logic layer
│   ├── mealPlanService.js     # Meal plan business logic
│   ├── nutritionService.js    # Nutrition analysis logic
│   └── userService.js         # User management logic
├── utils/                      # Utility functions
│   ├── prompts.js             # OpenAI prompt templates
│   ├── parsers.js             # Response parsing utilities
│   └── validators.js          # Joi validation schemas
├── triggers/                   # Firebase triggers
│   ├── userTriggers.js        # Firebase Auth triggers
│   └── index.js               # Trigger exports
└── package.json
```

## 🏗️ Architecture Layers

### 1. **Configuration Layer** (`config/`)
- **Purpose**: Centralized configuration and initialization
- **Files**:
  - `firebase.js`: Firebase Admin SDK initialization with singleton pattern
  - `openai.js`: OpenAI client configuration with lazy loading

### 2. **Middleware Layer** (`middleware/`)
- **Purpose**: Request processing and authentication
- **Files**:
  - `auth.js`: Firebase ID token verification middleware

### 3. **Routes Layer** (`routes/`)
- **Purpose**: HTTP request handling and response formatting
- **Files**:
  - `mealPlan.js`: Meal plan generation and retrieval endpoints
  - `nutrition.js`: Nutrition analysis endpoints
  - `user.js`: User profile management endpoints

### 4. **Services Layer** (`services/`)
- **Purpose**: Business logic and external API interactions
- **Files**:
  - `mealPlanService.js`: Meal plan generation, saving, and retrieval
  - `nutritionService.js`: Nutrition analysis and goal calculations
  - `userService.js`: User document management in Firestore

### 5. **Utils Layer** (`utils/`)
- **Purpose**: Reusable utility functions
- **Files**:
  - `validators.js`: Joi validation schemas for request validation
  - `prompts.js`: OpenAI prompt templates
  - `parsers.js`: Response parsing and data transformation

### 6. **Triggers Layer** (`triggers/`)
- **Purpose**: Firebase event handlers
- **Files**:
  - `userTriggers.js`: Firebase Auth event handlers
  - `index.js`: Centralized trigger exports

## 🔗 API Endpoints

### Meal Plan Endpoints
- `POST /generate-meal-plan` - Generate meal plan (public, for testing)
- `POST /meal-plans/generate` - Generate meal plan (authenticated)
- `GET /meal-plans/:userId` - Get user's meal plans (authenticated)

### Nutrition Endpoints
- `POST /analyze-nutrition` - Analyze nutrition content (authenticated)
- `POST /nutrition/analyze-with-goals` - Analyze with goal comparison (authenticated)
- `POST /nutrition/calculate-goals` - Calculate nutrition goals (authenticated)

### User Endpoints
- `GET /user/profile` - Get user profile (authenticated)
- `PUT /user/profile` - Update user profile (authenticated)
- `GET /user/:userId/profile` - Get specific user profile (authenticated)

### System Endpoints
- `GET /health` - Health check endpoint

## 🔧 Key Features

### Authentication
- Firebase ID token verification
- User ID extraction and validation
- Protected endpoints with middleware

### Validation
- Joi schema validation for all requests
- Type checking and default values
- Error handling with detailed messages

### Error Handling
- Centralized error handling middleware
- Consistent error response format
- Proper HTTP status codes

### Code Organization
- Separation of concerns
- Single responsibility principle
- Dependency injection pattern
- Modular architecture

## 🚀 Usage Examples

### Adding a New Endpoint

1. **Create validation schema** in `utils/validators.js`
2. **Add business logic** in appropriate service file
3. **Create route handler** in appropriate route file
4. **Register route** in `index.js`

### Adding a New Service

1. **Create service file** in `services/` directory
2. **Implement business logic** with proper error handling
3. **Export functions** for use in route handlers
4. **Add tests** (when test framework is added)

## 📝 Best Practices

### Code Style
- Use ESLint with Google style guide
- Consistent naming conventions
- Proper error handling
- Comprehensive logging

### Security
- Always validate input data
- Use authentication middleware for protected endpoints
- Sanitize user data before database operations
- Follow principle of least privilege

### Performance
- Lazy loading for external services
- Singleton pattern for configurations
- Efficient database queries
- Proper caching strategies

## 🔄 Migration Benefits

### Before (Monolithic)
- Single 420-line file
- Mixed concerns
- Difficult to maintain
- Hard to test individual components

### After (Clean Architecture)
- Modular structure with 15+ focused files
- Clear separation of concerns
- Easy to maintain and extend
- Testable components
- Reusable utilities
- Better error handling
- Consistent code style

## 🎯 Future Enhancements

1. **Testing Framework**: Add unit and integration tests
2. **Logging**: Implement structured logging
3. **Monitoring**: Add performance monitoring
4. **Caching**: Implement Redis caching for frequently accessed data
5. **Rate Limiting**: Add rate limiting middleware
6. **API Documentation**: Generate OpenAPI/Swagger documentation
7. **Database Migrations**: Add Firestore schema management
8. **Environment Configuration**: Add environment-specific configs
