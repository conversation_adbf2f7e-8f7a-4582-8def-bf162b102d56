const {getOpenAI} = require('../config/openai');
const {admin} = require('../config/firebase');
const {createMealPlanPrompt} = require('../utils/prompts');
const {parseMealPlan, extractCompletionContent} = require('../utils/parsers');

/**
 * Generates a meal plan using OpenAI
 * @param {Object} preferences - User dietary preferences
 * @param {number} duration - Number of days for the meal plan
 * @param {string} userId - User ID for saving the plan
 * @returns {Object} Generated meal plan
 */
async function generateMealPlan(preferences, duration, userId) {
  try {
    // Create prompt for OpenAI
    const prompt = createMealPlanPrompt(preferences, duration);

    // Generate meal plan using OpenAI
    const completion = await getOpenAI().chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: 'You are a professional nutritionist and meal planning expert. ' +
            'Generate detailed, balanced meal plans with accurate nutritional information.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 3000,
    });

    const mealPlanText = extractCompletionContent(completion);
    if (!mealPlanText) {
      throw new Error('Failed to generate meal plan');
    }

    // Parse and structure the meal plan
    const mealPlan = parseMealPlan(mealPlanText, preferences, duration);

    return mealPlan;
  } catch (error) {
    console.error('Error in generateMealPlan service:', error);
    throw error;
  }
}

/**
 * Saves a meal plan to Firestore
 * @param {string} userId - User ID
 * @param {Object} mealPlan - Generated meal plan
 * @param {Object} preferences - User preferences
 * @param {number} duration - Duration in days
 * @returns {Object} Saved document reference
 */
async function saveMealPlan(userId, mealPlan, preferences, duration) {
  try {
    const docRef = await admin.firestore()
      .collection('meal_plans')
      .add({
        userId,
        mealPlan,
        preferences,
        duration,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        status: 'active',
      });

    console.log('Meal plan saved with ID:', docRef.id);
    return docRef;
  } catch (error) {
    console.error('Error saving meal plan:', error);
    throw error;
  }
}

/**
 * Retrieves user's meal plans from Firestore
 * @param {string} userId - User ID
 * @param {Object} options - Query options (limit, status)
 * @returns {Array} Array of meal plans
 */
async function getUserMealPlans(userId, options = {}) {
  try {
    const {limit = 10, status = 'active'} = options;

    const mealPlansSnapshot = await admin.firestore()
      .collection('meal_plans')
      .where('userId', '==', userId)
      .where('status', '==', status)
      .orderBy('createdAt', 'desc')
      .limit(Number(limit))
      .get();

    const mealPlans = mealPlansSnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));

    return mealPlans;
  } catch (error) {
    console.error('Error fetching meal plans:', error);
    throw error;
  }
}

module.exports = {
  generateMealPlan,
  saveMealPlan,
  getUserMealPlans,
};
